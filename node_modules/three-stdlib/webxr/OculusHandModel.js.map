{"version": 3, "file": "OculusHandModel.js", "sources": ["../../src/webxr/OculusHandModel.ts"], "sourcesContent": ["import { Object3D, Sphere, Box3, Mesh, Texture, Vector3 } from 'three'\nimport { XRHandMeshModel } from './XRHandMeshModel'\n\nconst TOUCH_RADIUS = 0.01\nconst POINTING_JOINT = 'index-finger-tip'\n\nexport interface XRButton extends Object3D {\n  onPress(): void\n  onClear(): void\n  isPressed(): boolean\n  whilePressed(): void\n}\n\nclass OculusHandModel extends Object3D {\n  controller: Object3D\n  motionController: XRHandMeshModel | null\n  envMap: Texture | null\n  mesh: Mesh | null\n  xrInputSource: XRInputSource | null\n\n  constructor(controller: Object3D, leftModelPath?: string, rightModelPath?: string) {\n    super()\n\n    this.controller = controller\n    this.motionController = null\n    this.envMap = null\n\n    this.mesh = null\n    this.xrInputSource = null\n\n    controller.addEventListener('connected', (event) => {\n      const xrInputSource = (event as any).data\n\n      if (xrInputSource.hand && !this.motionController) {\n        this.xrInputSource = xrInputSource\n\n        this.motionController = new XRHandMeshModel(\n          this,\n          controller,\n          undefined,\n          xrInputSource.handedness,\n          xrInputSource.handedness === 'left' ? leftModelPath : rightModelPath,\n        )\n      }\n    })\n\n    controller.addEventListener('disconnected', () => {\n      this.dispose()\n    })\n  }\n\n  updateMatrixWorld(force?: boolean): void {\n    super.updateMatrixWorld(force)\n\n    if (this.motionController) {\n      this.motionController.updateMesh()\n    }\n  }\n\n  getPointerPosition(): Vector3 | null {\n    // @ts-ignore XRController needs to extend Group\n    const indexFingerTip = this.controller.joints[POINTING_JOINT]\n    if (indexFingerTip) {\n      return indexFingerTip.position\n    } else {\n      return null\n    }\n  }\n\n  intersectBoxObject(boxObject: Object3D): boolean {\n    const pointerPosition = this.getPointerPosition()\n    if (pointerPosition) {\n      const indexSphere = new Sphere(pointerPosition, TOUCH_RADIUS)\n      const box = new Box3().setFromObject(boxObject)\n      return indexSphere.intersectsBox(box)\n    } else {\n      return false\n    }\n  }\n\n  checkButton(button: XRButton): void {\n    if (this.intersectBoxObject(button)) {\n      button.onPress()\n    } else {\n      button.onClear()\n    }\n\n    if (button.isPressed()) {\n      button.whilePressed()\n    }\n  }\n\n  dispose(): void {\n    this.clear()\n    this.motionController = null\n  }\n}\n\nexport { OculusHandModel }\n"], "names": [], "mappings": ";;;;;;;;AAGA,MAAM,eAAe;AACrB,MAAM,iBAAiB;AASvB,MAAM,wBAAwB,SAAS;AAAA,EAOrC,YAAY,YAAsB,eAAwB,gBAAyB;AAC3E;AAPR;AACA;AACA;AACA;AACA;AAKE,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,SAAS;AAEd,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAEV,eAAA,iBAAiB,aAAa,CAAC,UAAU;AAClD,YAAM,gBAAiB,MAAc;AAErC,UAAI,cAAc,QAAQ,CAAC,KAAK,kBAAkB;AAChD,aAAK,gBAAgB;AAErB,aAAK,mBAAmB,IAAI;AAAA,UAC1B;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd,cAAc,eAAe,SAAS,gBAAgB;AAAA,QAAA;AAAA,MAE1D;AAAA,IAAA,CACD;AAEU,eAAA,iBAAiB,gBAAgB,MAAM;AAChD,WAAK,QAAQ;AAAA,IAAA,CACd;AAAA,EACH;AAAA,EAEA,kBAAkB,OAAuB;AACvC,UAAM,kBAAkB,KAAK;AAE7B,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB;IACxB;AAAA,EACF;AAAA,EAEA,qBAAqC;AAEnC,UAAM,iBAAiB,KAAK,WAAW,OAAO,cAAc;AAC5D,QAAI,gBAAgB;AAClB,aAAO,eAAe;AAAA,IAAA,OACjB;AACE,aAAA;AAAA,IACT;AAAA,EACF;AAAA,EAEA,mBAAmB,WAA8B;AACzC,UAAA,kBAAkB,KAAK;AAC7B,QAAI,iBAAiB;AACnB,YAAM,cAAc,IAAI,OAAO,iBAAiB,YAAY;AAC5D,YAAM,MAAM,IAAI,KAAK,EAAE,cAAc,SAAS;AACvC,aAAA,YAAY,cAAc,GAAG;AAAA,IAAA,OAC/B;AACE,aAAA;AAAA,IACT;AAAA,EACF;AAAA,EAEA,YAAY,QAAwB;AAC9B,QAAA,KAAK,mBAAmB,MAAM,GAAG;AACnC,aAAO,QAAQ;AAAA,IAAA,OACV;AACL,aAAO,QAAQ;AAAA,IACjB;AAEI,QAAA,OAAO,aAAa;AACtB,aAAO,aAAa;AAAA,IACtB;AAAA,EACF;AAAA,EAEA,UAAgB;AACd,SAAK,MAAM;AACX,SAAK,mBAAmB;AAAA,EAC1B;AACF;"}