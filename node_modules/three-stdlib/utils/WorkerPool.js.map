{"version": 3, "file": "WorkerPool.js", "sources": ["../../src/utils/WorkerPool.js"], "sourcesContent": ["/**\n * <AUTHOR> / https://github.com/deepkolos\n */\n\nexport class WorkerPool {\n  constructor(pool = 4) {\n    this.pool = pool\n    this.queue = []\n    this.workers = []\n    this.workersResolve = []\n    this.workerStatus = 0\n  }\n\n  _initWorker(workerId) {\n    if (!this.workers[workerId]) {\n      const worker = this.workerCreator()\n      worker.addEventListener('message', this._onMessage.bind(this, workerId))\n      this.workers[workerId] = worker\n    }\n  }\n\n  _getIdleWorker() {\n    for (let i = 0; i < this.pool; i++) if (!(this.workerStatus & (1 << i))) return i\n\n    return -1\n  }\n\n  _onMessage(workerId, msg) {\n    const resolve = this.workersResolve[workerId]\n    resolve && resolve(msg)\n\n    if (this.queue.length) {\n      const { resolve, msg, transfer } = this.queue.shift()\n      this.workersResolve[workerId] = resolve\n      this.workers[workerId].postMessage(msg, transfer)\n    } else {\n      this.workerStatus ^= 1 << workerId\n    }\n  }\n\n  setWorkerCreator(workerCreator) {\n    this.workerCreator = workerCreator\n  }\n\n  setWorkerLimit(pool) {\n    this.pool = pool\n  }\n\n  postMessage(msg, transfer) {\n    return new Promise((resolve) => {\n      const workerId = this._getIdleWorker()\n\n      if (workerId !== -1) {\n        this._initWorker(workerId)\n        this.workerStatus |= 1 << workerId\n        this.workersResolve[workerId] = resolve\n        this.workers[workerId].postMessage(msg, transfer)\n      } else {\n        this.queue.push({ resolve, msg, transfer })\n      }\n    })\n  }\n\n  dispose() {\n    this.workers.forEach((worker) => worker.terminate())\n    this.workersResolve.length = 0\n    this.workers.length = 0\n    this.queue.length = 0\n    this.workerStatus = 0\n  }\n}\n"], "names": ["resolve", "msg"], "mappings": "AAIO,MAAM,WAAW;AAAA,EACtB,YAAY,OAAO,GAAG;AACpB,SAAK,OAAO;AACZ,SAAK,QAAQ,CAAE;AACf,SAAK,UAAU,CAAE;AACjB,SAAK,iBAAiB,CAAE;AACxB,SAAK,eAAe;AAAA,EACrB;AAAA,EAED,YAAY,UAAU;AACpB,QAAI,CAAC,KAAK,QAAQ,QAAQ,GAAG;AAC3B,YAAM,SAAS,KAAK,cAAe;AACnC,aAAO,iBAAiB,WAAW,KAAK,WAAW,KAAK,MAAM,QAAQ,CAAC;AACvE,WAAK,QAAQ,QAAQ,IAAI;AAAA,IAC1B;AAAA,EACF;AAAA,EAED,iBAAiB;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM;AAAK,UAAI,EAAE,KAAK,eAAgB,KAAK;AAAK,eAAO;AAEhF,WAAO;AAAA,EACR;AAAA,EAED,WAAW,UAAU,KAAK;AACxB,UAAM,UAAU,KAAK,eAAe,QAAQ;AAC5C,eAAW,QAAQ,GAAG;AAEtB,QAAI,KAAK,MAAM,QAAQ;AACrB,YAAM,EAAE,SAAAA,UAAS,KAAAC,MAAK,SAAU,IAAG,KAAK,MAAM,MAAO;AACrD,WAAK,eAAe,QAAQ,IAAID;AAChC,WAAK,QAAQ,QAAQ,EAAE,YAAYC,MAAK,QAAQ;AAAA,IACtD,OAAW;AACL,WAAK,gBAAgB,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EAED,iBAAiB,eAAe;AAC9B,SAAK,gBAAgB;AAAA,EACtB;AAAA,EAED,eAAe,MAAM;AACnB,SAAK,OAAO;AAAA,EACb;AAAA,EAED,YAAY,KAAK,UAAU;AACzB,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAM,WAAW,KAAK,eAAgB;AAEtC,UAAI,aAAa,IAAI;AACnB,aAAK,YAAY,QAAQ;AACzB,aAAK,gBAAgB,KAAK;AAC1B,aAAK,eAAe,QAAQ,IAAI;AAChC,aAAK,QAAQ,QAAQ,EAAE,YAAY,KAAK,QAAQ;AAAA,MACxD,OAAa;AACL,aAAK,MAAM,KAAK,EAAE,SAAS,KAAK,UAAU;AAAA,MAC3C;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EAED,UAAU;AACR,SAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,WAAW;AACnD,SAAK,eAAe,SAAS;AAC7B,SAAK,QAAQ,SAAS;AACtB,SAAK,MAAM,SAAS;AACpB,SAAK,eAAe;AAAA,EACrB;AACH;"}