{"name": "@swc/core-linux-x64-gnu", "version": "1.13.0", "os": ["linux"], "cpu": ["x64"], "main": "swc.linux-x64-gnu.node", "files": ["swc.linux-x64-gnu.node", "swc"], "libc": ["glibc"], "description": "Super-fast alternative for babel", "keywords": ["swc", "swcpack", "babel", "typescript", "rust", "webpack", "tsc"], "author": "강동윤 <<EMAIL>>", "homepage": "https://swc.rs", "license": "Apache-2.0 AND MIT", "engines": {"node": ">=10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/swc-project/swc.git"}, "bugs": {"url": "https://github.com/swc-project/swc/issues"}}